#!/usr/bin/env python3
"""
Test suite for streaming JSON reader DuckDB extension.
Defines target functionality and expected behavior.
"""

import pytest
import duckdb
import json
import os
import tempfile
from typing import Dict, List, Any


class TestStreamingJsonReader:
    """Test suite for streaming JSON reader extension."""
    
    @pytest.fixture(scope="class")
    def duckdb_conn(self):
        """Setup DuckDB connection with extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        extension_path = "build/debug/streaming_json_reader.duckdb_extension"
        if not os.path.exists(extension_path):
            pytest.skip(f"Extension not found at {extension_path}")
        conn.execute(f'LOAD "{extension_path}"')
        return conn
    
    @pytest.fixture
    def temp_json_file(self):
        """Create temporary JSON file for testing."""
        fd, path = tempfile.mkstemp(suffix='.json')
        yield path
        os.close(fd)
        os.unlink(path)
    
    def create_json_file(self, data: Dict[str, Any], filepath: str):
        """Helper to create JSON test files."""
        with open(filepath, 'w') as f:
            json.dump(data, f)
    
    # Basic JSON Object Tests (0 levels of nesting)
    
    def test_simple_object_basic_types(self, duckdb_conn, temp_json_file):
        """Test simple JSON object with all basic types."""
        data = {
            "string_field": "test_value",
            "number_field": 42.5,
            "integer_field": 100,
            "boolean_field": True,
            "null_field": None
        }
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        # Verify structure
        assert len(result) == 1
        assert len(result[0]) == 5
        
        # Verify column types
        column_types = {col[0]: col[1] for col in columns}
        assert column_types["string_field"] == "VARCHAR"
        assert column_types["number_field"] == "DOUBLE"
        assert column_types["integer_field"] == "DOUBLE"
        assert column_types["boolean_field"] == "BOOLEAN"
        assert column_types["null_field"] == "VARCHAR"
        
        # Verify values
        row = result[0]
        assert row[0] == "test_value"  # string_field
        assert abs(row[1] - 42.5) < 0.001  # number_field
        assert abs(row[2] - 100.0) < 0.001  # integer_field
        assert row[3] is True  # boolean_field
        assert row[4] is None  # null_field
    
    def test_empty_object(self, duckdb_conn, temp_json_file):
        """Test empty JSON object."""
        data = {}
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        assert len(result) == 1
        assert len(columns) == 1  # Single "json" column for empty object
        assert columns[0][0] == "json"  # Column name should be "json"
        assert result[0][0] == "{}"  # Should contain empty object as string
    
    # One Level Nesting Tests
    
    def test_struct_field_access(self, duckdb_conn, temp_json_file):
        """Test STRUCT field access with dot notation."""
        data = {
            "metadata": {
                "name": "test_dataset",
                "version": "1.0",
                "count": 42
            },
            "simple_field": "value"
        }
        self.create_json_file(data, temp_json_file)
        
        # Test full object access
        result = duckdb_conn.execute(f'SELECT metadata FROM streaming_json_reader("{temp_json_file}")').fetchall()
        metadata = result[0][0]
        assert isinstance(metadata, dict)
        assert metadata["name"] == "test_dataset"
        assert metadata["version"] == "1.0"
        assert metadata["count"] == 42
        
        # Test STRUCT field access
        result = duckdb_conn.execute(f'SELECT metadata.name FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "test_dataset"
        
        result = duckdb_conn.execute(f'SELECT metadata.count FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert abs(result[0][0] - 42.0) < 0.001
    
    def test_array_basic_elements(self, duckdb_conn, temp_json_file):
        """Test array with basic element types."""
        data = {
            "numbers": [1, 2, 3, 4.5],
            "strings": ["a", "b", "c"],
            "booleans": [True, False, True]
        }
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        # Verify column types
        column_types = {col[0]: col[1] for col in columns}
        assert column_types["numbers"] == "DOUBLE[]"
        assert column_types["strings"] == "VARCHAR[]"
        assert column_types["booleans"] == "BOOLEAN[]"
        
        # Verify values
        row = result[0]
        assert row[0] == [1.0, 2.0, 3.0, 4.5]  # numbers
        assert row[1] == ["a", "b", "c"]  # strings
        assert row[2] == [True, False, True]  # booleans
    
    # Two Level Nesting Tests
    
    def test_nested_struct_access(self, duckdb_conn, temp_json_file):
        """Test nested STRUCT access."""
        data = {
            "config": {
                "database": {
                    "host": "localhost",
                    "port": 5432
                },
                "cache": {
                    "enabled": True,
                    "size": 1024
                }
            }
        }
        self.create_json_file(data, temp_json_file)
        
        # Test nested field access
        result = duckdb_conn.execute(f'SELECT config.database.host FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "localhost"
        
        result = duckdb_conn.execute(f'SELECT config.database.port FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert abs(result[0][0] - 5432.0) < 0.001
        
        result = duckdb_conn.execute(f'SELECT config.cache.enabled FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] is True
    
    def test_array_of_structs(self, duckdb_conn, temp_json_file):
        """Test array containing STRUCT elements."""
        data = {
            "users": [
                {"id": 1, "name": "Alice", "active": True},
                {"id": 2, "name": "Bob", "active": False},
                {"id": 3, "name": "Charlie", "active": True}
            ]
        }
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT users FROM streaming_json_reader("{temp_json_file}")').fetchall()
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        # Verify column type
        column_types = {col[0]: col[1] for col in columns}
        assert "STRUCT" in column_types["users"] and "[]" in column_types["users"]
        
        # Verify array access
        users = result[0][0]
        assert len(users) == 3
        assert users[0]["name"] == "Alice"
        assert users[1]["id"] == 2
        assert users[2]["active"] is True
        
        # Test array element access
        result = duckdb_conn.execute(f'SELECT users[1].name FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "Alice"  # DuckDB uses 1-based indexing
    
    # Three Level Nesting Tests
    
    def test_deep_nested_structures(self, duckdb_conn, temp_json_file):
        """Test three levels of nesting."""
        data = {
            "company": {
                "departments": {
                    "engineering": {
                        "head": "John Doe",
                        "budget": 1000000,
                        "projects": ["project_a", "project_b"]
                    }
                }
            }
        }
        self.create_json_file(data, temp_json_file)
        
        # Test deep field access
        result = duckdb_conn.execute(f'SELECT company.departments.engineering.head FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "John Doe"
        
        result = duckdb_conn.execute(f'SELECT company.departments.engineering.budget FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert abs(result[0][0] - 1000000.0) < 0.001

    # Projection Pushdown Tests

    def test_projection_pushdown_single_field(self, duckdb_conn, temp_json_file):
        """Test projection pushdown with single field selection."""
        data = {
            "field_a": "value_a",
            "field_b": "value_b",
            "field_c": "value_c"
        }
        self.create_json_file(data, temp_json_file)

        # Should only process field_a
        result = duckdb_conn.execute(f'SELECT field_a FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "value_a"

        # Verify only one column in result
        columns = duckdb_conn.execute(f'DESCRIBE SELECT field_a FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert len(columns) == 1
        assert columns[0][0] == "field_a"

    def test_projection_pushdown_struct_field(self, duckdb_conn, temp_json_file):
        """Test projection pushdown with STRUCT field selection."""
        data = {
            "metadata": {"name": "test", "version": "1.0"},
            "data": {"values": [1, 2, 3]},
            "unused": {"large": "data" * 1000}
        }
        self.create_json_file(data, temp_json_file)

        # Should only process metadata field
        result = duckdb_conn.execute(f'SELECT metadata FROM streaming_json_reader("{temp_json_file}")').fetchall()
        metadata = result[0][0]
        assert metadata["name"] == "test"
        assert metadata["version"] == "1.0"

    # Array Processing Tests

    def test_array_flattening(self, duckdb_conn, temp_json_file):
        """Test array flattening into rows."""
        # TODO it seems like this test does not follow the design decisions
        # Assess whether the query should actually be something containing an unnest
        data = [
            {"id": 1, "name": "Alice"},
            {"id": 2, "name": "Bob"},
            {"id": 3, "name": "Charlie"}
        ]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        # Should create 3 rows
        assert len(result) == 3
        assert result[0][0] == 1  # id
        assert result[0][1] == "Alice"  # name
        assert result[1][0] == 2
        assert result[1][1] == "Bob"
        assert result[2][0] == 3
        assert result[2][1] == "Charlie"

    @pytest.mark.skip(reason="Causes high disk usage")
    def test_nested_array_access(self, duckdb_conn, temp_json_file):
        """Test access to nested arrays."""
        data = {
            "matrix": [[1, 2], [3, 4], [5, 6]]
        }
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT matrix FROM streaming_json_reader("{temp_json_file}")').fetchall()
        matrix = result[0][0]
        assert matrix == [[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]]

        # Test element access
        result = duckdb_conn.execute(f'SELECT matrix[1][2] FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert abs(result[0][0] - 2.0) < 0.001

    # Memory Efficiency Tests

    def test_large_json_memory_efficiency(self, duckdb_conn, temp_json_file):
        """Test memory efficiency with large JSON files."""
        # Create large JSON with many fields
        data = {f"field_{i}": f"value_{i}" for i in range(1000)}
        data["target_field"] = "target_value"
        self.create_json_file(data, temp_json_file)

        # Should only process target_field, not all 1000 fields
        result = duckdb_conn.execute(f'SELECT target_field FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "target_value"

    def test_streaming_large_array(self, duckdb_conn, temp_json_file):
        """Test streaming processing of large arrays."""
        # Create array with many elements
        data = [{"id": i, "value": f"item_{i}"} for i in range(100)]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT COUNT(*) FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == 100

        # Test specific element access
        result = duckdb_conn.execute(f'SELECT id, value FROM streaming_json_reader("{temp_json_file}") WHERE id = 50').fetchall()
        assert len(result) == 1
        assert result[0][0] == 50
        assert result[0][1] == "item_50"

    # Error Handling Tests

    def test_malformed_json_error(self, duckdb_conn, temp_json_file):
        """Test error handling for malformed JSON."""
        with open(temp_json_file, 'w') as f:
            f.write('{"invalid": json,}')

        with pytest.raises(Exception) as exc_info:
            duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        assert "malformed" in str(exc_info.value).lower() or "json" in str(exc_info.value).lower()

    def test_nonexistent_file_error(self, duckdb_conn):
        """Test error handling for non-existent files."""
        with pytest.raises(Exception) as exc_info:
            duckdb_conn.execute('SELECT * FROM streaming_json_reader("nonexistent.json")').fetchall()

        assert "not found" in str(exc_info.value).lower() or "no such file" in str(exc_info.value).lower()

    def test_empty_file_handling(self, duckdb_conn, temp_json_file):
        """Test handling of empty files."""
        with open(temp_json_file, 'w') as f:
            f.write('')

        with pytest.raises(Exception) as exc_info:
            duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        assert "empty" in str(exc_info.value).lower() or "malformed" in str(exc_info.value).lower()

    # Data Type Precision Tests

    def test_numeric_precision(self, duckdb_conn, temp_json_file):
        """Test numeric precision handling."""
        data = {
            "small_int": 1,
            "large_int": 9223372036854775807,
            "small_float": 0.1,
            "large_float": 1.7976931348623157e+308,
            "scientific": 1.23e-10
        }
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        row = result[0]

        assert abs(row[0] - 1.0) < 0.001
        assert abs(row[1] - 9223372036854775807.0) < 1.0
        assert abs(row[2] - 0.1) < 0.001
        assert abs(row[4] - 1.23e-10) < 1e-15

    def test_boolean_values(self, duckdb_conn, temp_json_file):
        """Test boolean value handling."""
        data = {
            "true_val": True,
            "false_val": False
        }
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        row = result[0]

        assert row[0] is True
        assert row[1] is False

        # Test boolean column type
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        column_types = {col[0]: col[1] for col in columns}
        assert column_types["true_val"] == "BOOLEAN"
        assert column_types["false_val"] == "BOOLEAN"
