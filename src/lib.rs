extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    error::Error,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{<PERSON>sonRead<PERSON>, JsonStreamReader};



/// Represents a JSON value type with proper recursive semantics
#[derive(Debug, Clone, PartialEq)]
enum JsonValueType {
    Null,
    Boolean,
    Number,
    String,
    Array(Box<JsonValueType>),      // Array of elements of this type
    Object(Vec<JsonField>),         // Object with named fields
}

/// Represents a field in a JSON object
#[derive(Debug, Clone, PartialEq)]
struct JsonField {
    name: String,
    value_type: JsonValueType,
}



/// Represents the discovered JSON schema with recursive structure
#[derive(Debug, Clone)]
struct JsonSchema {
    root_type: JsonValueType,
    columns: Vec<StructuredColumn>,
}

/// Represents a column with proper structured types (STRUCT/ARRAY)
#[derive(Debug, Clone)]
struct StructuredColumn {
    name: String,
    value_type: JsonValueType,
}



#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    schema: JsonSchema,
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
    projected_columns: Vec<usize>, // Columns requested by the query
}

struct JsonReaderVTab;

// Helper function to discover JSON schema with proper recursive analysis
fn discover_json_schema(
    file_path: &str,
    projected_columns: Option<&[usize]>
) -> Result<JsonSchema, Box<dyn std::error::Error>> {
    eprintln!("!!! DISCOVER_JSON_SCHEMA CALLED WITH FILE: {}", file_path);
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Analyze the root JSON structure recursively
    let root_type = analyze_json_value(&mut json_reader)?;

    // Generate structured columns based on the discovered structure
    let columns = if let Some(projected) = projected_columns {
        // Query-driven: only generate columns for projected fields
        eprintln!("DEBUG SCHEMA: Using projected columns mode");
        generate_projected_columns(&root_type, projected)?
    } else {
        // Discovery mode: generate all possible columns
        eprintln!("DEBUG SCHEMA: Using discovery mode, calling generate_all_columns");
        eprintln!("DEBUG SCHEMA: Root type: {:?}", root_type);
        let cols = generate_all_columns(&root_type)?;
        eprintln!("DEBUG SCHEMA: Generated columns: {:?}", cols);
        cols
    };

    if columns.is_empty() {
        // Handle empty objects by creating a single column like DuckDB's built-in JSON reader
        // DuckDB requires table functions to return at least one column
        return Ok(JsonSchema {
            root_type,
            columns: vec![StructuredColumn {
                name: "json".to_string(),
                value_type: JsonValueType::String, // Will contain the JSON string representation
            }],
        });
    }

    Ok(JsonSchema {
        root_type,
        columns,
    })
}

// CRITICAL: NEVER convert STRUCT data to VARCHAR - this breaks the core design principle
// Use proper DuckDB STRUCT types and recursive vector handling
// VARCHAR fallbacks are temporary workarounds only for empty objects, not STRUCT data
//
// Recursively analyze JSON structure to build proper type representation
fn analyze_json_value(
    json_reader: &mut JsonStreamReader<BufReader<File>>
) -> Result<JsonValueType, Box<dyn std::error::Error>> {
    match json_reader.peek()? {
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(JsonValueType::Null)
        }
        struson::reader::ValueType::Boolean => {
            json_reader.next_bool()?;
            Ok(JsonValueType::Boolean)
        }
        struson::reader::ValueType::Number => {
            json_reader.next_number_as_str()?;
            Ok(JsonValueType::Number)
        }
        struson::reader::ValueType::String => {
            json_reader.next_string()?;
            Ok(JsonValueType::String)
        }
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;

            // Analyze first element to determine array element type
            let element_type = if json_reader.has_next()? {
                let first_element_type = analyze_json_value(json_reader)?;

                // Skip remaining elements for now (we could analyze more for union types)
                while json_reader.has_next()? {
                    json_reader.skip_value()?;
                }

                first_element_type
            } else {
                // Empty array - assume string elements
                JsonValueType::String
            };

            json_reader.end_array()?;
            Ok(JsonValueType::Array(Box::new(element_type)))
        }
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;
            let mut fields = Vec::new();

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let field_type = analyze_json_value(json_reader)?;

                fields.push(JsonField {
                    name: field_name,
                    value_type: field_type,
                });
            }

            json_reader.end_object()?;
            Ok(JsonValueType::Object(fields))
        }
    }
}

// Generate columns for projected fields only
fn generate_projected_columns(
    _root_type: &JsonValueType,
    _projected: &[usize]
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    // TODO: Implement query-driven column generation
    Err("Query-driven column generation not yet implemented".into())
}

// Generate structured columns from JSON structure (preserving hierarchy)
fn generate_all_columns(
    root_type: &JsonValueType
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    match root_type {
        JsonValueType::Object(fields) => {
            // Root object: each field becomes a top-level column
            let mut columns = Vec::new();
            for field in fields {
                columns.push(StructuredColumn {
                    name: field.name.clone(),
                    value_type: field.value_type.clone(),
                });
            }
            Ok(columns)
        }
        JsonValueType::Array(element_type) => {
            // Root array: flatten array elements into rows
            eprintln!("DEBUG SCHEMA: Root array detected, flattening elements into rows");
            match element_type.as_ref() {
                JsonValueType::Object(fields) => {
                    // Array of objects: each object field becomes a column
                    eprintln!("DEBUG SCHEMA: Array of objects with {} fields", fields.len());
                    let mut columns = Vec::new();
                    for field in fields {
                        eprintln!("DEBUG SCHEMA: Adding column '{}' with type {:?}", field.name, field.value_type);
                        columns.push(StructuredColumn {
                            name: field.name.clone(),
                            value_type: field.value_type.clone(),
                        });
                    }
                    eprintln!("DEBUG SCHEMA: Generated {} flattened columns", columns.len());
                    Ok(columns)
                }
                _ => {
                    // Array of primitives: single column with element type
                    eprintln!("DEBUG SCHEMA: Array of primitives, creating single 'value' column");
                    Ok(vec![StructuredColumn {
                        name: "value".to_string(),
                        value_type: element_type.as_ref().clone(),
                    }])
                }
            }
        }
        _ => {
            // Root primitive: single column
            Ok(vec![StructuredColumn {
                name: "value".to_string(),
                value_type: root_type.clone(),
            }])
        }
    }
}

// CRITICAL: NEVER convert STRUCT data to VARCHAR - this breaks the core design principle
// Use proper DuckDB STRUCT types and recursive vector handling
// VARCHAR fallbacks are temporary workarounds only for empty objects, not STRUCT data
//
// Convert JsonValueType to DuckDB LogicalTypeHandle
fn json_type_to_duckdb_type(json_type: &JsonValueType) -> Result<LogicalTypeHandle, Box<dyn std::error::Error>> {
    match json_type {
        JsonValueType::Boolean => Ok(LogicalTypeHandle::from(LogicalTypeId::Boolean)),
        JsonValueType::Number => Ok(LogicalTypeHandle::from(LogicalTypeId::Double)), // Use proper Double type for numbers
        JsonValueType::String => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Null => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Array(element_type) => {
            // Create proper LIST type with correct element type
            let element_logical_type = json_type_to_duckdb_type(element_type)?;
            Ok(LogicalTypeHandle::list(&element_logical_type))
        }
        JsonValueType::Object(fields) => {
            // Create proper STRUCT type for nested objects
            let mut struct_fields = Vec::new();

            for field in fields {
                let field_type = json_type_to_duckdb_type(&field.value_type)?;
                struct_fields.push((field.name.as_str(), field_type));
            }

            Ok(LogicalTypeHandle::struct_type(&struct_fields))
        }
    }
}

// Apply projection pushdown to only include requested columns
fn apply_projection_pushdown(
    full_schema: &JsonSchema,
    projected_column_names: &[String],
) -> JsonSchema {
    eprintln!("DEBUG PROJECTION: Applying pushdown for columns: {:?}", projected_column_names);

    // If no specific projection, return full schema
    if projected_column_names.is_empty() {
        eprintln!("DEBUG PROJECTION: No projection specified, using full schema");
        return full_schema.clone();
    }

    // Filter columns to only include projected ones
    let mut projected_columns = Vec::new();

    for column_name in projected_column_names {
        if let Some(column) = full_schema.columns.iter().find(|col| &col.name == column_name) {
            eprintln!("DEBUG PROJECTION: Including column: {}", column_name);
            projected_columns.push(column.clone());
        } else {
            eprintln!("DEBUG PROJECTION: Warning - requested column '{}' not found in schema", column_name);
        }
    }

    JsonSchema {
        root_type: full_schema.root_type.clone(),
        columns: projected_columns,
    }
}

// Read JSON file using streaming parser and write directly to DuckDB vectors for memory efficiency
fn read_json_streaming_to_vectors(
    file_path: &str,
    schema: &JsonSchema,
    output: &DataChunkHandle,
) -> Result<usize, Box<dyn std::error::Error>> {
    use std::fs::File;
    use struson::reader::{JsonReader, JsonStreamReader};

    eprintln!("DEBUG STREAMING: Opening file for streaming JSON parsing: {}", file_path);

    // Validate file exists and is readable
    if !std::path::Path::new(file_path).exists() {
        return Err(format!("JSON file not found: {}", file_path).into());
    }

    // Open file for streaming with error handling
    let file = match File::open(file_path) {
        Ok(f) => f,
        Err(e) => return Err(format!("Failed to open JSON file '{}': {}", file_path, e).into()),
    };

    let mut json_reader = JsonStreamReader::new(file);
    let mut row_count = 0;

    // Start reading the JSON structure with comprehensive error handling
    match json_reader.peek() {
        Ok(value_type) => match value_type {
            struson::reader::ValueType::Object => {
                eprintln!("DEBUG STREAMING: Processing single JSON object");
                match read_object_streaming_to_vectors(&mut json_reader, schema, output, row_count) {
                    Ok(()) => row_count += 1,
                    Err(e) => return Err(format!("Error parsing JSON object: {}", e).into()),
                }
            }
            struson::reader::ValueType::Array => {
                eprintln!("DEBUG STREAMING: Processing JSON array");
                match json_reader.begin_array() {
                    Ok(_) => {
                        while json_reader.has_next().unwrap_or(false) {
                            if row_count > 10000 {
                                eprintln!("DEBUG STREAMING: Warning - processing large array with {} elements", row_count + 1);
                            }

                            // For root-level arrays, each element becomes a row with flattened columns
                            match read_array_element_as_row(&mut json_reader, schema, output, row_count) {
                                Ok(()) => row_count += 1,
                                Err(e) => {
                                    eprintln!("DEBUG STREAMING: Warning - skipping malformed array element at position {}: {}", row_count + 1, e);
                                    // Try to skip the malformed element
                                    if let Err(skip_err) = json_reader.skip_value() {
                                        return Err(format!("Failed to skip malformed element: {}", skip_err).into());
                                    }
                                }
                            }
                        }

                        if let Err(e) = json_reader.end_array() {
                            return Err(format!("Error closing JSON array: {}", e).into());
                        }
                    }
                    Err(e) => return Err(format!("Error starting JSON array: {}", e).into()),
                }
            }
            _ => {
                return Err(format!("Unsupported JSON root type: expected object or array, found {:?}", value_type).into());
            }
        },
        Err(e) => {
            return Err(format!("Failed to read JSON file '{}': {} (file may be empty or malformed)", file_path, e).into());
        }
    }

    eprintln!("DEBUG STREAMING: Successfully parsed {} rows using streaming", row_count);

    if row_count == 0 {
        eprintln!("DEBUG STREAMING: Warning - no valid data rows found in JSON file");
    }

    Ok(row_count)
}

// Read a single array element and write it as a flattened row
fn read_array_element_as_row(
    json_reader: &mut JsonStreamReader<File>,
    schema: &JsonSchema,
    output: &DataChunkHandle,
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Object => {
            // Object element: read fields and map to flattened columns
            json_reader.begin_object()?;

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                eprintln!("DEBUG ARRAY_FLATTEN: Processing field: {}", field_name);

                // Find the column index for this field in the flattened schema
                if let Some(col_idx) = schema.columns.iter().position(|col| col.name == field_name) {
                    eprintln!("DEBUG ARRAY_FLATTEN: Found column {} for field {}", col_idx, field_name);

                    // Read the field value directly into the appropriate column
                    match read_value_streaming_to_vector(json_reader, output, col_idx, row_idx, &schema.columns[col_idx].value_type) {
                        Ok(()) => {
                            eprintln!("DEBUG ARRAY_FLATTEN: Successfully inserted field '{}' at column {}", field_name, col_idx);
                        }
                        Err(e) => {
                            eprintln!("DEBUG ARRAY_FLATTEN: Error inserting field '{}': {}", field_name, e);
                            // Set null value for this field
                            set_vector_null(output, col_idx, row_idx, &schema.columns[col_idx].value_type);
                            // Try to skip the problematic value
                            if let Err(skip_err) = json_reader.skip_value() {
                                return Err(format!("Failed to skip malformed field '{}': {}", field_name, skip_err).into());
                            }
                        }
                    }
                } else {
                    // Field not in schema - skip it
                    eprintln!("DEBUG ARRAY_FLATTEN: Skipping unknown field: {}", field_name);
                    json_reader.skip_value()?;
                }
            }

            json_reader.end_object()?;
            Ok(())
        }
        _ => {
            // Primitive element: write to single column (assuming schema has one column)
            if schema.columns.len() == 1 {
                read_value_streaming_to_vector(json_reader, output, 0, row_idx, &schema.columns[0].value_type)
            } else {
                Err("Primitive array element but schema has multiple columns".into())
            }
        }
    }
}

// Read a JSON object using streaming parser and write directly to DuckDB vectors
fn read_object_streaming_to_vectors(
    json_reader: &mut JsonStreamReader<File>,
    schema: &JsonSchema,
    output: &DataChunkHandle,
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    eprintln!("DEBUG STREAMING: Reading object to vectors at row {}", row_idx);

    // Begin object with error handling
    if let Err(e) = json_reader.begin_object() {
        return Err(format!("Failed to start reading JSON object: {}", e).into());
    }

    // Check if this is an empty object case (single "json" column for empty objects)
    let is_empty_object_case = schema.columns.len() == 1 &&
                               schema.columns[0].name == "json" &&
                               matches!(schema.columns[0].value_type, JsonValueType::String);

    let mut field_count = 0;
    while json_reader.has_next().unwrap_or(false) {
        field_count += 1;

        // Prevent infinite loops on malformed JSON
        if field_count > 1000 {
            return Err("JSON object has too many fields (>1000), possible malformed JSON".into());
        }

        let field_name = match json_reader.next_name() {
            Ok(name) => name.to_string(),
            Err(e) => return Err(format!("Failed to read field name at position {}: {}", field_count, e).into()),
        };

        // Find the column index for this field
        if let Some(col_idx) = schema.columns.iter().position(|col| col.name == field_name) {
            // Only parse fields that are in our schema (projection pushdown)
            eprintln!("DEBUG PROJECTION: Parsing required field: {}", field_name);
            match read_value_streaming_to_vector(json_reader, output, col_idx, row_idx, &schema.columns[col_idx].value_type) {
                Ok(()) => {
                    eprintln!("DEBUG STREAMING: Successfully parsed field '{}' at column {}", field_name, col_idx);
                }
                Err(e) => {
                    eprintln!("DEBUG ERROR: Failed to parse field '{}': {}", field_name, e);
                    // Set null value for this field
                    set_vector_null(output, col_idx, row_idx, &schema.columns[col_idx].value_type);
                    // Try to skip the problematic value
                    if let Err(skip_err) = json_reader.skip_value() {
                        return Err(format!("Failed to skip malformed field '{}': {}", field_name, skip_err).into());
                    }
                }
            }
        } else {
            // Skip fields not in schema (projection optimization)
            eprintln!("DEBUG PROJECTION: Skipping unrequested field: {}", field_name);
            if let Err(e) = json_reader.skip_value() {
                return Err(format!("Failed to skip unrequested field '{}': {}", field_name, e).into());
            }
        }
    }

    // End object with error handling
    if let Err(e) = json_reader.end_object() {
        return Err(format!("Failed to close JSON object: {}", e).into());
    }

    // Handle empty object case - set JSON string representation
    if is_empty_object_case && field_count == 0 {
        eprintln!("DEBUG STREAMING: Empty object detected, setting JSON string representation");
        let mut vector = output.flat_vector(0);
        let cstring = std::ffi::CString::new("{}")?;
        vector.insert(row_idx, cstring);
    }

    Ok(())
}

// Unified recursive JSON value insertion system
// This is the core function that handles ALL JSON nesting patterns uniformly
fn insert_json_value_recursive(
    json_reader: &mut JsonStreamReader<File>,
    vector_context: VectorContext,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Null => {
            json_reader.next_null()?;
            set_vector_null_unified(&vector_context, row_idx, expected_type);
            Ok(())
        }
        ValueType::Boolean => {
            let b = json_reader.next_bool()?;
            match &vector_context {
                VectorContext::DataChunk(output, col_idx) => {
                    insert_primitive_to_data_chunk(output, *col_idx, row_idx, &TempValue::Boolean(b))?;
                }
                _ => return Err("Invalid vector context for primitive boolean".into()),
            }
            Ok(())
        }
        ValueType::Number => {
            let number_str = json_reader.next_number_as_str()?;
            let number_value: f64 = number_str.parse().unwrap_or(0.0);
            match &vector_context {
                VectorContext::DataChunk(output, col_idx) => {
                    insert_primitive_to_data_chunk(output, *col_idx, row_idx, &TempValue::Number(number_value))?;
                }
                _ => return Err("Invalid vector context for primitive number".into()),
            }
            Ok(())
        }
        ValueType::String => {
            let s = json_reader.next_string()?;
            match &vector_context {
                VectorContext::DataChunk(output, col_idx) => {
                    insert_primitive_to_data_chunk(output, *col_idx, row_idx, &TempValue::String(s))?;
                }
                _ => return Err("Invalid vector context for primitive string".into()),
            }
            Ok(())
        }
        ValueType::Array => {
            insert_array_recursive(json_reader, vector_context, row_idx, expected_type)
        }
        ValueType::Object => {
            insert_object_recursive(json_reader, vector_context, row_idx, expected_type)
        }
    }
}

// Unified vector context that can represent any DuckDB vector type
enum VectorContext<'a> {
    DataChunk(&'a DataChunkHandle, usize), // (output, col_idx)
    FlatVector(&'a mut duckdb::core::FlatVector),
    ListVector(&'a mut duckdb::core::ListVector),
    StructVector(&'a mut duckdb::core::StructVector),
}

// Read a JSON value using streaming parser and write directly to DuckDB vector
// This is now a wrapper around the unified recursive system
fn read_value_streaming_to_vector(
    json_reader: &mut JsonStreamReader<File>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    let vector_context = VectorContext::DataChunk(output, col_idx);
    insert_json_value_recursive(json_reader, vector_context, row_idx, expected_type)
}

// Unified null value insertion
fn set_vector_null_unified(
    vector_context: &VectorContext,
    row_idx: usize,
    value_type: &JsonValueType,
) {
    match vector_context {
        VectorContext::DataChunk(output, col_idx) => {
            set_vector_null(output, *col_idx, row_idx, value_type);
        }
        VectorContext::FlatVector(_vector) => {
            // Cannot set null on immutable reference - this needs redesign
            eprintln!("DEBUG: Cannot set null on FlatVector reference");
        }
        VectorContext::ListVector(_vector) => {
            // Cannot set null on immutable reference - this needs redesign
            eprintln!("DEBUG: Cannot set null on ListVector reference");
        }
        VectorContext::StructVector(_vector) => {
            // Cannot set null on immutable reference - this needs redesign
            eprintln!("DEBUG: Cannot set null on StructVector reference");
        }
    }
}

// Unified primitive value insertion - redesigned to avoid borrowing issues
fn insert_primitive_to_data_chunk(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    temp_value: &TempValue,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut vector = output.flat_vector(col_idx);
    insert_temp_value_to_vector(&mut vector, row_idx, temp_value, &JsonValueType::String)
}


// Unified array insertion
fn insert_array_recursive(
    json_reader: &mut JsonStreamReader<File>,
    vector_context: VectorContext,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    if let JsonValueType::Array(element_type) = expected_type {
        match vector_context {
            VectorContext::DataChunk(output, col_idx) => {
                read_array_streaming_to_vector(json_reader, output, col_idx, row_idx, expected_type)
            }
            VectorContext::ListVector(_list_vector) => {
                // Handle array within existing list context - this is multi-dimensional arrays
                // For now, return error - will implement this next
                Err("Multi-dimensional arrays not yet implemented".into())
            }
            _ => {
                Err("Invalid vector context for array insertion".into())
            }
        }
    } else {
        Err("Expected array type but got different type".into())
    }
}

// Unified object insertion
fn insert_object_recursive(
    json_reader: &mut JsonStreamReader<File>,
    vector_context: VectorContext,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    if let JsonValueType::Object(fields) = expected_type {
        match vector_context {
            VectorContext::DataChunk(output, col_idx) => {
                read_object_streaming_to_struct_vector(json_reader, output, col_idx, row_idx, expected_type)
            }
            VectorContext::StructVector(_struct_vector) => {
                // Handle object within existing struct context - this is nested objects
                // For now, return error - will implement this next
                Err("Nested objects in struct context not yet implemented".into())
            }
            _ => {
                Err("Invalid vector context for object insertion".into())
            }
        }
    } else {
        Err("Expected object type but got different type".into())
    }
}

// Read array from streaming parser and write directly to DuckDB list vector
fn read_array_streaming_to_vector(
    json_reader: &mut JsonStreamReader<File>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    if let JsonValueType::Array(element_type) = expected_type {
        let mut list_vector = output.list_vector(col_idx);
        json_reader.begin_array()?;

        match element_type.as_ref() {
            JsonValueType::Object(fields) => {
                // Array of objects - implement proper streaming solution
                eprintln!("DEBUG ARRAY: Processing array of objects with {} fields", fields.len());

                // Collect all array elements first (we need to know the count for DuckDB)
                let mut object_elements = Vec::new();
                while json_reader.has_next()? {
                    match json_reader.peek()? {
                        struson::reader::ValueType::Object => {
                            // Read the object into a temporary structure
                            let obj_data = read_object_to_temp_structure(json_reader, fields)?;
                            object_elements.push(Some(obj_data));
                        }
                        struson::reader::ValueType::Null => {
                            json_reader.next_null()?;
                            object_elements.push(None);
                        }
                        _ => {
                            // Skip invalid elements
                            json_reader.skip_value()?;
                            object_elements.push(None);
                        }
                    }
                }

                json_reader.end_array()?;

                // Set up the list entry
                list_vector.set_entry(row_idx, 0, object_elements.len());

                // Get the struct child vector for the array elements
                let mut struct_child_vector = list_vector.struct_child(object_elements.len());

                // Insert each object into the struct vector
                for (elem_idx, obj_data) in object_elements.iter().enumerate() {
                    if let Some(field_values) = obj_data {
                        // Insert each field of this object
                        for (field_idx, field) in fields.iter().enumerate() {
                            let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());

                            if let Some(field_value) = field_values.get(&field.name) {
                                insert_temp_value_to_vector(&mut field_vector, elem_idx, field_value, &field.value_type)?;
                            } else {
                                field_vector.set_null(elem_idx);
                            }
                        }
                    } else {
                        // Null object - set all fields as null
                        for field_idx in 0..fields.len() {
                            let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());
                            field_vector.set_null(elem_idx);
                        }
                    }
                }

                eprintln!("DEBUG ARRAY: Successfully processed {} object elements", object_elements.len());
            }
            JsonValueType::Array(nested_element_type) => {
                // Multi-dimensional array - array of arrays
                eprintln!("DEBUG ARRAY: Processing multi-dimensional array with nested element type: {:?}", nested_element_type);

                // Collect all nested arrays using the new recursive approach
                let mut nested_arrays = Vec::new();
                while json_reader.has_next()? {
                    match json_reader.peek()? {
                        struson::reader::ValueType::Array => {
                            // Process nested array recursively
                            eprintln!("DEBUG ARRAY: Processing nested array element");

                            // Use the new recursive data collection
                            let nested_array_data = collect_nested_array_data_recursive(json_reader, nested_element_type)?;
                            nested_arrays.push(Some(nested_array_data));
                        }
                        struson::reader::ValueType::Null => {
                            json_reader.next_null()?;
                            nested_arrays.push(None);
                        }
                        _ => {
                            // Skip invalid elements
                            json_reader.skip_value()?;
                            nested_arrays.push(None);
                        }
                    }
                }

                json_reader.end_array()?;

                // CORRECT APPROACH: Follow DuckDB pattern
                // 1. First collect all data and insert into child vector
                // 2. Then set up list entries with proper offsets

                eprintln!("DEBUG ROW: Processing row_idx={}, nested_arrays.len()={}", row_idx, nested_arrays.len());

                // Get the list child vector for nested arrays
                let mut nested_list_vector = list_vector.list_child();

                // CORRECT FIX: Separate array offset from element offset
                let array_offset = row_idx * nested_arrays.len();  // Offset for arrays in nested list vector
                let element_offset = row_idx * 4;  // Offset for elements in child vector (approximation)

                // Insert nested array data with proper element offset
                insert_nested_arrays_with_row_offset(&mut nested_list_vector, &nested_arrays, nested_element_type, element_offset)?;

                // Set up the list entry for this row with array offset (not element offset)
                list_vector.set_entry(row_idx, array_offset, nested_arrays.len());
                eprintln!("DEBUG ROW: Set row {} entry: array_offset={}, length={}",
                         row_idx, array_offset, nested_arrays.len());

                eprintln!("DEBUG ARRAY: Successfully processed {} nested arrays", nested_arrays.len());
            }
            _ => {
                // Array of primitives - collect and process
                let mut elements = Vec::new();
                while json_reader.has_next()? {
                    match json_reader.peek()? {
                        struson::reader::ValueType::Null => {
                            json_reader.next_null()?;
                            elements.push(None);
                        }
                        struson::reader::ValueType::Boolean => {
                            let b = json_reader.next_bool()?;
                            elements.push(Some(b.to_string()));
                        }
                        struson::reader::ValueType::Number => {
                            let number_str = json_reader.next_number_as_str()?;
                            elements.push(Some(number_str.to_string()));
                        }
                        struson::reader::ValueType::String => {
                            let s = json_reader.next_string()?;
                            elements.push(Some(s));
                        }
                        _ => {
                            // Skip complex types in primitive arrays
                            json_reader.skip_value()?;
                            elements.push(None);
                        }
                    }
                }

                json_reader.end_array()?;

                // Set up the list entry
                list_vector.set_entry(row_idx, 0, elements.len());

                // Insert primitive elements based on type
                match element_type.as_ref() {
                    JsonValueType::String => {
                        let mut child_vector = list_vector.child(elements.len());
                        for (i, elem) in elements.iter().enumerate() {
                            if let Some(s) = elem {
                                let cstring = std::ffi::CString::new(s.as_str())?;
                                child_vector.insert(i, cstring);
                            } else {
                                child_vector.set_null(i);
                            }
                        }
                    }
                    JsonValueType::Number => {
                        let mut child_vector = list_vector.child(elements.len());
                        for (i, elem) in elements.iter().enumerate() {
                            if let Some(s) = elem {
                                let n: f64 = s.parse().unwrap_or(0.0);
                                let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(elements.len());
                                data_slice[i] = n;
                            } else {
                                child_vector.set_null(i);
                            }
                        }
                    }
                    JsonValueType::Boolean => {
                        let mut child_vector = list_vector.child(elements.len());
                        for (i, elem) in elements.iter().enumerate() {
                            if let Some(s) = elem {
                                let b: bool = s.parse().unwrap_or(false);
                                let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(elements.len());
                                data_slice[i] = b;
                            } else {
                                child_vector.set_null(i);
                            }
                        }
                    }
                    _ => {
                        // For other primitive types, set all as null
                        let mut child_vector = list_vector.child(elements.len());
                        for i in 0..elements.len() {
                            child_vector.set_null(i);
                        }
                    }
                }
            }
        }

        Ok(())
    } else {
        Err("Expected array type but got different type".into())
    }
}

// Temporary value type for collecting object data during streaming
#[derive(Debug, Clone)]
enum TempValue {
    Null,
    Boolean(bool),
    Number(f64),
    String(String),
}

// Read object from streaming parser into temporary structure
fn read_object_to_temp_structure(
    json_reader: &mut JsonStreamReader<File>,
    fields: &[JsonField],
) -> Result<std::collections::HashMap<String, TempValue>, Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    let mut field_values = std::collections::HashMap::new();

    json_reader.begin_object()?;

    while json_reader.has_next()? {
        let field_name = json_reader.next_name()?.to_string();

        // Only collect fields that are in our schema
        if fields.iter().any(|f| f.name == field_name) {
            let temp_value = match json_reader.peek()? {
                struson::reader::ValueType::Null => {
                    json_reader.next_null()?;
                    TempValue::Null
                }
                struson::reader::ValueType::Boolean => {
                    let b = json_reader.next_bool()?;
                    TempValue::Boolean(b)
                }
                struson::reader::ValueType::Number => {
                    let number_str = json_reader.next_number_as_str()?;
                    let number_value: f64 = number_str.parse().unwrap_or(0.0);
                    TempValue::Number(number_value)
                }
                struson::reader::ValueType::String => {
                    let s = json_reader.next_string()?;
                    TempValue::String(s)
                }
                _ => {
                    // For complex types (objects, arrays), skip for now
                    json_reader.skip_value()?;
                    TempValue::Null
                }
            };

            field_values.insert(field_name, temp_value);
        } else {
            // Skip fields not in schema
            json_reader.skip_value()?;
        }
    }

    json_reader.end_object()?;

    Ok(field_values)
}

// Insert temporary value into DuckDB vector
fn insert_temp_value_to_vector(
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    temp_value: &TempValue,
    _expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    match temp_value {
        TempValue::Null => {
            vector.set_null(row_idx);
        }
        TempValue::Boolean(b) => {
            let data_slice: &mut [bool] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = *b;
        }
        TempValue::Number(n) => {
            let data_slice: &mut [f64] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = *n;
        }
        TempValue::String(s) => {
            let cstring = std::ffi::CString::new(s.as_str())?;
            vector.insert(row_idx, cstring);
        }
    }

    Ok(())
}

// Read object from streaming parser and write directly to DuckDB struct vector
fn read_object_streaming_to_struct_vector(
    json_reader: &mut JsonStreamReader<File>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    if let JsonValueType::Object(fields) = expected_type {
        let mut struct_vector = output.struct_vector(col_idx);
        read_object_streaming_to_struct_recursive(json_reader, &mut struct_vector, fields, row_idx)
    } else {
        Err("Expected object type but got different type".into())
    }
}

// Recursive helper to read object fields directly into struct vector using streaming
fn read_object_streaming_to_struct_recursive(
    json_reader: &mut JsonStreamReader<File>,
    struct_vector: &mut duckdb::core::StructVector,
    fields: &[JsonField],
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    eprintln!("DEBUG STREAMING: Reading object to struct with {} fields at row {}", fields.len(), row_idx);

    json_reader.begin_object()?;

    while json_reader.has_next()? {
        let field_name = json_reader.next_name()?.to_string();
        eprintln!("DEBUG STREAMING: Processing struct field: {}", field_name);

        // Find the field in our schema
        if let Some(field_idx) = fields.iter().position(|f| f.name == field_name) {
            let field = &fields[field_idx];
            eprintln!("DEBUG STREAMING: Found field {} at index {}", field_name, field_idx);

            match &field.value_type {
                JsonValueType::Object(nested_fields) => {
                    // Recursive struct handling
                    let mut nested_struct_vector = struct_vector.struct_vector_child(field_idx);
                    if let Err(e) = read_object_streaming_to_struct_recursive(json_reader, &mut nested_struct_vector, nested_fields, 0) {
                        eprintln!("DEBUG STREAMING: Error in nested struct {}: {}", field_name, e);
                        nested_struct_vector.set_null(0);
                    }
                }
                JsonValueType::Array(element_type) => {
                    // Array field handling within STRUCT - implement complete solution
                    eprintln!("DEBUG STREAMING: Processing array field {} with element type: {:?}", field_name, element_type);
                    let mut list_vector = struct_vector.list_vector_child(field_idx);

                    match insert_array_within_struct(json_reader, &mut list_vector, element_type) {
                        Ok(()) => {
                            eprintln!("DEBUG STREAMING: Successfully processed array field {}", field_name);
                        }
                        Err(e) => {
                            eprintln!("DEBUG STREAMING: Error processing array field {}: {}", field_name, e);
                            list_vector.set_null(0);
                        }
                    }
                }
                _ => {
                    // Primitive field - read directly into child vector
                    let mut field_vector = struct_vector.child(field_idx, 1);
                    match read_primitive_streaming_to_vector(json_reader, &mut field_vector, 0, &field.value_type) {
                        Ok(()) => {
                            eprintln!("DEBUG STREAMING: Successfully inserted primitive field {}", field_name);
                        }
                        Err(e) => {
                            eprintln!("DEBUG STREAMING: Error inserting primitive field {}: {}", field_name, e);
                            field_vector.set_null(0);
                        }
                    }
                }
            }
        } else {
            // Field not in schema - skip it
            eprintln!("DEBUG STREAMING: Skipping unknown field: {}", field_name);
            json_reader.skip_value()?;
        }
    }

    json_reader.end_object()?;
    Ok(())
}

// Enhanced temporary value type that can handle nested arrays
#[derive(Debug, Clone)]
enum NestedTempValue {
    Null,
    Boolean(bool),
    Number(f64),
    String(String),
    Array(Vec<NestedTempValue>), // Recursive array support
}

// Collect nested array data for multi-dimensional arrays with true recursion
fn collect_nested_array_data_recursive(
    json_reader: &mut JsonStreamReader<File>,
    element_type: &JsonValueType,
) -> Result<Vec<NestedTempValue>, Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    let mut elements = Vec::new();
    json_reader.begin_array()?;

    while json_reader.has_next()? {
        match json_reader.peek()? {
            struson::reader::ValueType::Null => {
                json_reader.next_null()?;
                elements.push(NestedTempValue::Null);
            }
            struson::reader::ValueType::Boolean => {
                let b = json_reader.next_bool()?;
                elements.push(NestedTempValue::Boolean(b));
            }
            struson::reader::ValueType::Number => {
                let number_str = json_reader.next_number_as_str()?;
                let number_value: f64 = number_str.parse().unwrap_or(0.0);
                elements.push(NestedTempValue::Number(number_value));
            }
            struson::reader::ValueType::String => {
                let s = json_reader.next_string()?;
                elements.push(NestedTempValue::String(s));
            }
            struson::reader::ValueType::Array => {
                // Recursive array processing - handle arbitrary depth
                eprintln!("DEBUG NESTED: Processing deeper nested array");
                if let JsonValueType::Array(deeper_element_type) = element_type {
                    let nested_array = collect_nested_array_data_recursive(json_reader, deeper_element_type)?;
                    elements.push(NestedTempValue::Array(nested_array));
                } else {
                    // Type mismatch - skip
                    json_reader.skip_value()?;
                    elements.push(NestedTempValue::Null);
                }
            }
            _ => {
                // Skip other complex types
                json_reader.skip_value()?;
                elements.push(NestedTempValue::Null);
            }
        }
    }

    json_reader.end_array()?;
    Ok(elements)
}


// Simple fix: Insert nested arrays with proper row offset to avoid memory conflicts
fn insert_nested_arrays_with_row_offset(
    nested_list_vector: &mut duckdb::core::ListVector,
    nested_arrays: &[Option<Vec<NestedTempValue>>],
    element_type: &JsonValueType,
    row_offset: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    eprintln!("DEBUG ROW_OFFSET: Processing {} nested arrays with row_offset={}",
              nested_arrays.len(), row_offset);

    match element_type {
        JsonValueType::Number => {
            // Calculate total elements for this row
            let row_total_elements: usize = nested_arrays.iter()
                .map(|arr_opt| arr_opt.as_ref().map_or(0, |arr| arr.len()))
                .sum();

            if row_total_elements > 0 {
                // Get child vector with capacity for this row + previous rows
                let total_capacity = row_offset + row_total_elements;
                let mut child_vector = nested_list_vector.child(total_capacity);
                let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(total_capacity);

                // Insert data starting at row_offset
                let mut current_pos = row_offset;
                for nested_array_opt in nested_arrays {
                    if let Some(nested_array) = nested_array_opt {
                        for temp_value in nested_array {
                            match temp_value {
                                NestedTempValue::Number(n) => {
                                    data_slice[current_pos] = *n;
                                    eprintln!("DEBUG ROW_OFFSET: Set data_slice[{}] = {}", current_pos, n);
                                    current_pos += 1;
                                }
                                _ => {
                                    data_slice[current_pos] = 0.0;
                                    current_pos += 1;
                                }
                            }
                        }
                    }
                }
            }

            // Set up list entries with proper offsets within this row
            // The array_idx here should be the global array index, not local to this row
            let mut element_offset = row_offset;
            for (local_array_idx, nested_array_opt) in nested_arrays.iter().enumerate() {
                // Calculate global array index based on row and local position
                let global_array_idx = (row_offset / 4) * nested_arrays.len() + local_array_idx;

                if let Some(nested_array) = nested_array_opt {
                    nested_list_vector.set_entry(global_array_idx, element_offset, nested_array.len());
                    eprintln!("DEBUG ROW_OFFSET: Set entry[{}] offset={}, length={}",
                             global_array_idx, element_offset, nested_array.len());
                    element_offset += nested_array.len();
                } else {
                    nested_list_vector.set_null(global_array_idx);
                    eprintln!("DEBUG ROW_OFFSET: Set entry[{}] as null", global_array_idx);
                }
            }
        }
        _ => {
            eprintln!("DEBUG ROW_OFFSET: Unsupported element type");
            for array_idx in 0..nested_arrays.len() {
                nested_list_vector.set_null(array_idx);
            }
        }
    }

    Ok(())
}

// CORRECT APPROACH: Process multi-dimensional arrays with proper chunk-level coordination
// This follows DuckDB's streaming pattern: process all rows in current chunk together
fn process_multidimensional_arrays_for_chunk(
    list_vector: &mut duckdb::core::ListVector,
    all_row_data: &[Vec<Option<Vec<NestedTempValue>>>],
    element_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    eprintln!("DEBUG CHUNK: Processing {} rows with multi-dimensional arrays", all_row_data.len());

    match element_type {
        JsonValueType::Number => {
            // Step 1: Calculate total capacity needed across ALL rows
            let total_child_capacity: usize = all_row_data.iter()
                .map(|row_arrays| {
                    row_arrays.iter()
                        .map(|arr_opt| arr_opt.as_ref().map_or(0, |arr| arr.len()))
                        .sum::<usize>()
                })
                .sum();

            eprintln!("DEBUG CHUNK: Total child capacity needed: {}", total_child_capacity);

            // Step 2: Get child vector with total capacity for entire chunk
            let mut nested_list_vector = list_vector.list_child();

            if total_child_capacity > 0 {
                let mut child_vector = nested_list_vector.child(total_child_capacity);
                let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(total_child_capacity);

                // Step 3: Insert all data and set up offsets
                let mut global_offset = 0;

                for (row_idx, row_arrays) in all_row_data.iter().enumerate() {
                    let row_start_offset = global_offset;

                    // Insert data for this row
                    for nested_array_opt in row_arrays {
                        if let Some(nested_array) = nested_array_opt {
                            for temp_value in nested_array {
                                match temp_value {
                                    NestedTempValue::Number(n) => {
                                        data_slice[global_offset] = *n;
                                        eprintln!("DEBUG CHUNK: Set data_slice[{}] = {}", global_offset, n);
                                        global_offset += 1;
                                    }
                                    _ => {
                                        data_slice[global_offset] = 0.0;
                                        global_offset += 1;
                                    }
                                }
                            }
                        }
                    }

                    // Set up list entry for this row
                    let row_length = row_arrays.len();
                    list_vector.set_entry(row_idx, row_start_offset, row_length);
                    eprintln!("DEBUG CHUNK: Row {} entry: offset={}, length={}",
                             row_idx, row_start_offset, row_length);

                    // Set up nested array entries within this row
                    let mut row_offset = row_start_offset;
                    for (array_idx, nested_array_opt) in row_arrays.iter().enumerate() {
                        if let Some(nested_array) = nested_array_opt {
                            nested_list_vector.set_entry(row_offset + array_idx, row_offset, nested_array.len());
                            row_offset += nested_array.len();
                        } else {
                            nested_list_vector.set_null(row_offset + array_idx);
                        }
                    }
                }
            }
        }
        _ => {
            eprintln!("DEBUG CHUNK: Unsupported element type for chunk processing");
            // Set all rows as null for unsupported types
            for row_idx in 0..all_row_data.len() {
                list_vector.set_null(row_idx);
            }
        }
    }

    Ok(())
}

// Insert nested array data recursively into list vector with proper offset management
fn insert_nested_array_recursive_with_offset(
    list_vector: &mut duckdb::core::ListVector,
    array_idx: usize,
    offset: usize,
    nested_data: &[NestedTempValue],
    element_type: &JsonValueType,
    total_capacity: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    eprintln!("DEBUG RECURSIVE: array_idx={}, offset={}, nested_data.len()={}, element_type={:?}",
              array_idx, offset, nested_data.len(), element_type);

    // Set up the list entry for this specific nested array with correct offset
    list_vector.set_entry(array_idx, offset, nested_data.len());
    eprintln!("DEBUG RECURSIVE: Set entry for array_idx={} with offset={}, length={}",
              array_idx, offset, nested_data.len());

    match element_type {
        JsonValueType::Array(deeper_element_type) => {
            // Multi-dimensional: each element is another array
            let mut deeper_list_vector = list_vector.list_child();

            // For multi-dimensional arrays, calculate total capacity for deeper level
            let deeper_total_capacity: usize = nested_data.iter()
                .map(|val| match val {
                    NestedTempValue::Array(arr) => arr.len(),
                    _ => 0,
                })
                .sum();

            // For multi-dimensional arrays, we need to calculate offsets for the deeper level too
            let mut deeper_offset = 0;
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::Array(deeper_array) => {
                        // Recursively insert the deeper array with proper offset
                        let child_array_idx = offset + elem_idx;
                        insert_nested_array_recursive_with_offset(&mut deeper_list_vector, child_array_idx, deeper_offset, deeper_array, deeper_element_type, deeper_total_capacity)?;
                        deeper_offset += deeper_array.len();
                    }
                    NestedTempValue::Null => {
                        let child_array_idx = offset + elem_idx;
                        deeper_list_vector.set_null(child_array_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        let child_array_idx = offset + elem_idx;
                        deeper_list_vector.set_null(child_array_idx);
                    }
                }
            }
        }
        JsonValueType::Number => {
            // Array of numbers - insert directly with proper capacity and offset
            let mut child_vector = list_vector.child(total_capacity);
            eprintln!("DEBUG RECURSIVE: Got child vector with total_capacity={}", total_capacity);

            // First pass: set null values at correct positions
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                let actual_idx = offset + elem_idx;
                match nested_value {
                    NestedTempValue::Null | _ if !matches!(nested_value, NestedTempValue::Number(_)) => {
                        child_vector.set_null(actual_idx);
                    }
                    _ => {} // Handle numbers in second pass
                }
            }

            // Second pass: set number values using slice with correct offset
            let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(total_capacity);
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                if let NestedTempValue::Number(n) = nested_value {
                    let actual_idx = offset + elem_idx;
                    eprintln!("DEBUG RECURSIVE: Setting data_slice[{}] = {}", actual_idx, n);
                    data_slice[actual_idx] = *n;
                }
            }
        }
        JsonValueType::String => {
            // Array of strings - insert directly
            let mut child_vector = list_vector.child(nested_data.len());
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::String(s) => {
                        let cstring = std::ffi::CString::new(s.as_str())?;
                        child_vector.insert(elem_idx, cstring);
                    }
                    NestedTempValue::Null => {
                        child_vector.set_null(elem_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(elem_idx);
                    }
                }
            }
        }
        JsonValueType::Boolean => {
            // Array of booleans - insert directly
            let mut child_vector = list_vector.child(nested_data.len());
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::Boolean(b) => {
                        let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(nested_data.len());
                        data_slice[elem_idx] = *b;
                    }
                    NestedTempValue::Null => {
                        child_vector.set_null(elem_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(elem_idx);
                    }
                }
            }
        }
        _ => {
            // For other types, set all as null
            let mut child_vector = list_vector.child(nested_data.len());
            for elem_idx in 0..nested_data.len() {
                child_vector.set_null(elem_idx);
            }
        }
    }

    Ok(())
}

// Insert array within struct context - handles arrays as fields of objects
fn insert_array_within_struct(
    json_reader: &mut JsonStreamReader<File>,
    list_vector: &mut duckdb::core::ListVector,
    element_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    eprintln!("DEBUG ARRAY_IN_STRUCT: Processing array with element type: {:?}", element_type);

    json_reader.begin_array()?;

    match element_type {
        JsonValueType::Object(fields) => {
            // Array of objects within struct - collect and process
            let mut object_elements = Vec::new();
            while json_reader.has_next()? {
                match json_reader.peek()? {
                    struson::reader::ValueType::Object => {
                        let obj_data = read_object_to_temp_structure(json_reader, fields)?;
                        object_elements.push(Some(obj_data));
                    }
                    struson::reader::ValueType::Null => {
                        json_reader.next_null()?;
                        object_elements.push(None);
                    }
                    _ => {
                        json_reader.skip_value()?;
                        object_elements.push(None);
                    }
                }
            }

            json_reader.end_array()?;

            // Set up the list entry for row 0 (since this is within a struct)
            list_vector.set_entry(0, 0, object_elements.len());

            // Get the struct child vector for the array elements
            let struct_child_vector = list_vector.struct_child(object_elements.len());

            // Insert each object into the struct vector
            for (elem_idx, obj_data) in object_elements.iter().enumerate() {
                if let Some(field_values) = obj_data {
                    // Insert each field of this object
                    for (field_idx, field) in fields.iter().enumerate() {
                        let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());

                        if let Some(field_value) = field_values.get(&field.name) {
                            insert_temp_value_to_vector(&mut field_vector, elem_idx, field_value, &field.value_type)?;
                        } else {
                            field_vector.set_null(elem_idx);
                        }
                    }
                } else {
                    // Null object - set all fields as null
                    for field_idx in 0..fields.len() {
                        let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());
                        field_vector.set_null(elem_idx);
                    }
                }
            }

            eprintln!("DEBUG ARRAY_IN_STRUCT: Successfully processed {} object elements", object_elements.len());
        }
        JsonValueType::Array(_nested_element_type) => {
            // Multi-dimensional array - array of arrays
            eprintln!("DEBUG ARRAY_IN_STRUCT: Multi-dimensional array detected");

            // For now, implement basic support - count elements and set up structure
            let mut array_count = 0;
            while json_reader.has_next()? {
                // For multi-dimensional arrays, we need more complex handling
                // For now, skip the nested arrays and count them
                json_reader.skip_value()?;
                array_count += 1;
            }

            json_reader.end_array()?;

            // Set up the list entry
            list_vector.set_entry(0, 0, array_count);

            // For now, set all nested arrays as null - this needs full implementation
            let mut nested_list_vector = list_vector.list_child();
            for i in 0..array_count {
                nested_list_vector.set_null(i);
            }

            eprintln!("DEBUG ARRAY_IN_STRUCT: Multi-dimensional array with {} elements (set as null for now)", array_count);
        }
        _ => {
            // Array of primitives within struct
            let mut elements = Vec::new();
            while json_reader.has_next()? {
                match json_reader.peek()? {
                    struson::reader::ValueType::Null => {
                        json_reader.next_null()?;
                        elements.push(None);
                    }
                    struson::reader::ValueType::Boolean => {
                        let b = json_reader.next_bool()?;
                        elements.push(Some(b.to_string()));
                    }
                    struson::reader::ValueType::Number => {
                        let number_str = json_reader.next_number_as_str()?;
                        elements.push(Some(number_str.to_string()));
                    }
                    struson::reader::ValueType::String => {
                        let s = json_reader.next_string()?;
                        elements.push(Some(s));
                    }
                    _ => {
                        json_reader.skip_value()?;
                        elements.push(None);
                    }
                }
            }

            json_reader.end_array()?;

            // Set up the list entry
            list_vector.set_entry(0, 0, elements.len());

            // Insert primitive elements
            match element_type {
                JsonValueType::String => {
                    let mut child_vector = list_vector.child(elements.len());
                    for (i, elem) in elements.iter().enumerate() {
                        if let Some(s) = elem {
                            let cstring = std::ffi::CString::new(s.as_str())?;
                            child_vector.insert(i, cstring);
                        } else {
                            child_vector.set_null(i);
                        }
                    }
                }
                JsonValueType::Number => {
                    let mut child_vector = list_vector.child(elements.len());
                    for (i, elem) in elements.iter().enumerate() {
                        if let Some(s) = elem {
                            let n: f64 = s.parse().unwrap_or(0.0);
                            let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(elements.len());
                            data_slice[i] = n;
                        } else {
                            child_vector.set_null(i);
                        }
                    }
                }
                JsonValueType::Boolean => {
                    let mut child_vector = list_vector.child(elements.len());
                    for (i, elem) in elements.iter().enumerate() {
                        if let Some(s) = elem {
                            let b: bool = s.parse().unwrap_or(false);
                            let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(elements.len());
                            data_slice[i] = b;
                        } else {
                            child_vector.set_null(i);
                        }
                    }
                }
                _ => {
                    // For other types, set all as null
                    let mut child_vector = list_vector.child(elements.len());
                    for i in 0..elements.len() {
                        child_vector.set_null(i);
                    }
                }
            }

            eprintln!("DEBUG ARRAY_IN_STRUCT: Successfully processed {} primitive elements", elements.len());
        }
    }

    Ok(())
}

// Read primitive value from streaming parser and write directly to vector
fn read_primitive_streaming_to_vector(
    json_reader: &mut JsonStreamReader<File>,
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    value_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Null => {
            json_reader.next_null()?;
            vector.set_null(row_idx);
            Ok(())
        }
        ValueType::Boolean => {
            let b = json_reader.next_bool()?;
            let data_slice: &mut [bool] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = b;
            Ok(())
        }
        ValueType::Number => {
            let number_str = json_reader.next_number_as_str()?;
            let number_value: f64 = number_str.parse().unwrap_or(0.0);
            let data_slice: &mut [f64] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = number_value;
            Ok(())
        }
        ValueType::String => {
            let s = json_reader.next_string()?;
            let cstring = std::ffi::CString::new(s)?;
            vector.insert(row_idx, cstring);
            Ok(())
        }
        _ => {
            // For non-primitive types, set as null
            json_reader.skip_value()?;
            vector.set_null(row_idx);
            Ok(())
        }
    }
}

// Helper function to set null values in vectors based on type
fn set_vector_null(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    value_type: &JsonValueType,
) {
    match value_type {
        JsonValueType::Object(_) => {
            let mut struct_vector = output.struct_vector(col_idx);
            struct_vector.set_null(row_idx);
        }
        JsonValueType::Array(_) => {
            let mut list_vector = output.list_vector(col_idx);
            list_vector.set_null(row_idx);
        }
        _ => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_idx);
        }
    }
}

// CRITICAL: NEVER convert STRUCT data to VARCHAR - this breaks the core design principle
// Use proper DuckDB STRUCT types and recursive vector handling
// VARCHAR fallbacks are temporary workarounds only for empty objects, not STRUCT data




impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        eprintln!("DEBUG BIND: File path: {}", file_path);
        eprintln!("DEBUG BIND: Parameter count: {}", bind.get_parameter_count());

        // Try to explore what other information might be available in bind phase
        eprintln!("DEBUG BIND: Exploring BindInfo for additional projection information...");

        // Let's see if BindInfo has any methods that might give us nested field information
        // This is exploratory to understand what's available
        eprintln!("DEBUG BIND: Checking for nested field projection capabilities...");

        // Discover JSON schema from the file (no projection info available at bind time)
        let full_schema = match discover_json_schema(&file_path, None) {
            Ok(schema) => {
                eprintln!("DEBUG BIND: Discovered full schema: {:?}", schema);
                schema
            },
            Err(e) => {
                eprintln!("DEBUG BIND: Schema discovery failed: {}", e);
                return Err(e);
            }
        };

        // At bind time, we don't have projection info yet, so use full schema
        // Projection optimization will be applied during data reading
        eprintln!("DEBUG BIND: Using full schema at bind time (projection applied later)");

        // Add result columns to DuckDB based on full schema
        for (i, column) in full_schema.columns.iter().enumerate() {
            eprintln!("DEBUG BIND: Adding column '{}' at index {}", column.name, i);

            // Convert JSON types to DuckDB logical types (including STRUCT/ARRAY)
            let logical_type = json_type_to_duckdb_type(&column.value_type)?;

            bind.add_result_column(&column.name, logical_type);
            eprintln!("DEBUG BIND: Added '{}' as {:?} type", column.name, column.value_type);
        }

        Ok(JsonReaderBindData {
            file_path,
            schema: full_schema,
        })
    }

    fn init(init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Test projection pushdown capabilities
        eprintln!("DEBUG: Testing projection pushdown capabilities");

        // Get column indices for projection pushdown
        let column_indices = init.get_column_indices();
        eprintln!("DEBUG: Projected column indices: {:?}", column_indices);
        eprintln!("DEBUG: Number of projected columns: {}", column_indices.len());

        // Get bind data to access column names
        let bind_data = unsafe { &*(init.get_bind_data() as *const JsonReaderBindData) };
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG: All available columns: {:?}", column_names);

        // Map indices to actual column names
        let projected_column_names: Vec<String> = column_indices
            .iter()
            .map(|&idx| {
                if (idx as usize) < column_names.len() {
                    column_names[idx as usize].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", idx)
                }
            })
            .collect();

        eprintln!("DEBUG: Projected column names: {:?}", projected_column_names);

        // Try to explore what other methods might be available on InitInfo
        eprintln!("DEBUG: Exploring InitInfo methods...");

        // Let's try to see if there are any other methods we can call on InitInfo
        // This is exploratory - we'll see what's available

        // Check if there's any way to get more detailed projection information
        eprintln!("DEBUG: Checking for additional projection information...");

        // Let's see if there are any other methods we can call
        // (This is exploratory - some might not exist)

        if column_indices.is_empty() {
            eprintln!("DEBUG: No specific columns projected (SELECT * query?)");
        } else {
            eprintln!("DEBUG: Specific columns requested: {:?}", projected_column_names);
        }

        // Convert column indices to simple usize vector
        let projected_columns: Vec<usize> = column_indices
            .iter()
            .map(|&idx| idx as usize)
            .collect();

        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
            projected_columns,
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = unsafe { &*(func.get_bind_data() as *const JsonReaderBindData) };

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        eprintln!("DEBUG FUNC: Processing file: {}", bind_data.file_path);
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG FUNC: Available columns: {:?}", column_names);

        // Apply projection optimization based on projected columns
        let projected_column_names: Vec<String> = init_data.projected_columns
            .iter()
            .map(|&col_idx| {
                if col_idx < column_names.len() {
                    column_names[col_idx].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", col_idx)
                }
            })
            .collect();

        eprintln!("DEBUG FUNC: Projected columns for optimization: {:?}", projected_column_names);

        // Apply projection pushdown to schema
        let optimized_schema = apply_projection_pushdown(&bind_data.schema, &projected_column_names);
        eprintln!("DEBUG FUNC: Using optimized schema: {:?}", optimized_schema);

        // Read JSON file using pure streaming and write directly to DuckDB vectors
        eprintln!("DEBUG FUNC: Reading JSON file with pure streaming to vectors");

        match read_json_streaming_to_vectors(&bind_data.file_path, &optimized_schema, output) {
            Ok(row_count) => {
                eprintln!("DEBUG FUNC: Successfully processed {} rows using pure streaming", row_count);

                if row_count == 0 {
                    output.set_len(0);
                } else {
                    output.set_len(row_count);
                    eprintln!("DEBUG FUNC: Set output length to {} rows", row_count);
                }

                // Mark as finished after processing the data
                init_data.finished.store(true, Ordering::Relaxed);
                eprintln!("DEBUG FUNC: Marked processing as finished");
            }
            Err(e) => {
                eprintln!("ERROR: Failed to read JSON file '{}': {}", bind_data.file_path, e);

                // Provide helpful error messages based on error type
                let error_msg = if e.to_string().contains("not found") {
                    format!("JSON file not found: {}", bind_data.file_path)
                } else if e.to_string().contains("malformed") || e.to_string().contains("Failed to read") {
                    format!("Malformed JSON in file: {} - {}", bind_data.file_path, e)
                } else if e.to_string().contains("permission") || e.to_string().contains("Permission denied") {
                    format!("Permission denied reading file: {}", bind_data.file_path)
                } else if e.to_string().contains("too many fields") {
                    format!("JSON object too complex in file: {} - {}", bind_data.file_path, e)
                } else {
                    format!("Error reading JSON file '{}': {}", bind_data.file_path, e)
                };

                output.set_len(0);
                init_data.finished.store(true, Ordering::Relaxed);
                eprintln!("DEBUG FUNC: Marked processing as finished due to error");
                return Err(error_msg.into());
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}